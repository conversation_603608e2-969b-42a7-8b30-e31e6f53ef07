{"version": 3, "file": "ClientInfoStore.js", "sourceRoot": "", "sources": ["../src/ClientInfoStore.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,YAAY,CAAC;AAC5B,OAAO,EAAE,mBAAmB,EAAE,SAAS,EAAE,MAAM,YAAY,CAAC;AAC5D,OAAO,MAAM,MAAM,qBAAqB,CAAC;AACzC,OAAO,EAAE,UAAU,EAAE,MAAM,wBAAwB,CAAC;AAEpD,MAAM,OAAO,eAAe;IAClB,UAAU,CAAsB;IAChC,WAAW,CAAa;IAEhC,YAAmB,SAA8B,EAAE,UAAuB;QACxE,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC;QAC5B,IAAI,CAAC,WAAW,GAAG,UAAU,IAAI,IAAI,UAAU,CAAC;YAC9C,EAAE,EAAE,SAAS;YACb,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAEM,KAAK,GAAG,GAAS,EAAE;QACxB,IAAI,IAAI,CAAC,UAAU,KAAK,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YACrD,IAAI,CAAC,WAAW,GAAG,SAAS,CAAC;QAC/B,CAAC;IACH,CAAC,CAAC;IAEF,kGAAkG;IAClG,WAAW;IACX,IAAW,UAAU;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;YAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC;gBAChC,EAAE,EAAE,SAAS;gBACb,gBAAgB,EAAE,UAAU,CAAC,gBAAgB,EAAE;gBAC/C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;QACD,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED,IAAW,UAAU;QACnB,aAAa;QACb,iCAAiC;QACjC,yCAAyC;QACzC,+BAA+B;QAC/B,KAAK;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IACrC,CAAC;IAED,IAAW,QAAQ;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;IACnC,CAAC;IAED,IAAW,gBAAgB;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;IAC3C,CAAC;IAED,IAAW,eAAe;QACxB,4BAA4B;QAC5B,2CAA2C;QAC3C,sCAAsC;QACtC,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;IAC1C,CAAC;IAEM,MAAM,CAAC,gBAAgB;QAC5B,OAAO,MAAM,CAAC,UAAU,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,kGAAkG;IAClG,eAAe;IACR,KAAK,CAAC,IAAI,CAAC,OAA6B;QAC7C,IAAI,OAAO,EAAE,CAAC;YACZ,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;gBACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,OAAO,CAAC,QAAQ,CAAC;YAC/C,CAAC;iBAAM,IAAI,OAAO,CAAC,QAAQ,KAAK,IAAI,EAAE,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC;YACnC,CAAC;YAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC7B,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;YAC/D,CAAC;YAED,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;YACjD,CAAC;iBAAM,IAAI,OAAO,CAAC,SAAS,KAAK,IAAI,EAAE,CAAC;gBACtC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;YACpC,CAAC;YAED,IAAI,OAAO,CAAC,eAAe,EAAE,CAAC;gBAC5B,IAAI,CAAC,WAAW,CAAC,eAAe,GAAG,OAAO,CAAC,eAAe,CAAC;YAC7D,CAAC;iBAAM,IAAI,OAAO,CAAC,eAAe,KAAK,IAAI,EAAE,CAAC;gBAC5C,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;YAC1C,CAAC;YAED,IAAI,OAAO,CAAC,kBAAkB,EAAE,CAAC;gBAC/B,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,OAAO,CAAC,kBAAkB,CAAC;YACnE,CAAC;iBAAM,IAAI,OAAO,CAAC,kBAAkB,KAAK,IAAI,EAAE,CAAC;gBAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC;YAC7C,CAAC;YAED,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;gBAChC,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,OAAO,CAAC,mBAAmB,CAAC;YACrE,CAAC;iBAAM,IAAI,OAAO,CAAC,mBAAmB,KAAK,IAAI,EAAE,CAAC;gBAChD,OAAO,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC;YAC9C,CAAC;YAED,IAAI,OAAO,CAAC,gBAAgB,EAAE,CAAC;gBAC7B,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,OAAO,CAAC,gBAAgB,CAAC;YAC/D,CAAC;iBAAM,IAAI,OAAO,CAAC,gBAAgB,KAAK,IAAI,EAAE,CAAC;gBAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;YAC3C,CAAC;YAED,IAAI,OAAO,CAAC,cAAc,EAAE,CAAC;gBAC3B,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,OAAO,CAAC,cAAc,CAAC;YAC3D,CAAC;iBAAM,IAAI,OAAO,CAAC,cAAc,KAAK,IAAI,EAAE,CAAC;gBAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;YACzC,CAAC;QACH,CAAC;QAED,gGAAgG;QAChG,aAAa;QACb,IAAI,IAAI,CAAC,UAAU,KAAK,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;QAED,gGAAgG;QAChG,gBAAgB;QAChB,IACE,IAAI,CAAC,UAAU,KAAK,mBAAmB,CAAC,YAAY;YACpD,CAAC,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EACvD,CAAC;YACD,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;gBAC9C,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;YAClD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,oDAAoD,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAChF,CAAC;YAED,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;QAED,gGAAgG;QAChG,MAAM;QACN,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,OAAO,CAClC,IAAI,CAAC,WAAW,EAChB,SAAS,CAAC,UAAU,CACrB,CAAC;YAEF,IAAI,WAAW,CAAC,KAAK,EAAE,CAAC;gBACtB,MAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YACxD,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,oDAAoD,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAChF,CAAC;QAED,OAAO,IAAI,CAAC,UAAU,CAAC;IACzB,CAAC;IAEM,KAAK,CAAC,IAAI;QACf,gGAAgG;QAChG,aAAa;QACb,IAAI,IAAI,CAAC,UAAU,KAAK,mBAAmB,CAAC,QAAQ,EAAE,CAAC;YACrD,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;QAED,gGAAgG;QAChG,gBAAgB;QAChB,IACE,IAAI,CAAC,UAAU,KAAK,mBAAmB,CAAC,YAAY;YACpD,CAAC,OAAO,MAAM,KAAK,WAAW,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,EACvD,CAAC;YACD,IAAI,CAAC;gBACH,MAAM,IAAI,GAAG,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;gBACxD,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;gBAEpD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB,EAAE,CAAC;oBAC5D,MAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE,EAAE,IAAI,EAAE,CAAC,CAAC;oBACvE,OAAO,IAAI,CAAC,UAAU,CAAC;gBACzB,CAAC;gBAED,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;gBAC7D,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,CAAC;QACH,CAAC;QAED,gGAAgG;QAChG,MAAM;QACN,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,MAAM,EAAE,CAAC,QAAQ,CACnC,SAAS,EACT,SAAS,CAAC,UAAU,CACrB,CAAC;YAEF,IACE,WAAW,CAAC,KAAK;gBACjB,CAAC,WAAW;gBACZ,CAAC,WAAW,CAAC,MAAM,EACnB,CAAC;gBACD,MAAM,CAAC,KAAK,CAAC,iDAAiD,EAAE,EAAE,WAAW,EAAE,CAAC,CAAC;gBACjF,OAAO,IAAI,CAAC,UAAU,CAAC;YACzB,CAAC;YAED,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;YAEtD,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;YAC7D,OAAO,IAAI,CAAC,UAAU,CAAC;QACzB,CAAC;IACH,CAAC;IAAA,CAAC;IAEK,KAAK,CAAC,KAAK;QAChB,IAAI,CAAC,WAAW,GAAG,IAAI,UAAU,EAAE,CAAC;QAEpC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAEM,KAAK,CAAC,yBAAyB,CAAC,eAAwB;QAC7D,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,IAAI,CAAC;QACjC,IAAI,CAAC,WAAW,CAAC,SAAS,GAAG,IAAI,CAAC;QAClC,IAAI,CAAC,WAAW,CAAC,eAAe,GAAG,eAAe,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;QACvF,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG,IAAI,CAAC;QAC3C,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG,IAAI,CAAC;QAC5C,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC;QAEvC,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;IACrC,CAAC;IAEM,qBAAqB,GAAG,CAAC,eAAuB,EAAQ,EAAE;QAC/D,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,kBAAkB,EAAE,CAAC;YACzC,IAAI,CAAC,WAAW,CAAC,kBAAkB,GAAG;gBACpC,EAAE,EAAE,GAAG;gBACP,eAAe;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,kBAAkB,CAAC,eAAe,GAAG,eAAe,CAAC;QACxE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,mBAAmB,EAAE,CAAC;YAC1C,IAAI,CAAC,WAAW,CAAC,mBAAmB,GAAG;gBACrC,EAAE,EAAE,GAAG;gBACP,eAAe;gBACf,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,eAAe,GAAG,eAAe,CAAC;QACzE,CAAC;QACD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,wEAAwE,EAAE,KAAK,CAAC,CAAC;QAChG,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEK,YAAY,GAAG,GAAS,EAAE;QAC/B,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;QACzC,IAAI,CAAC,WAAW,CAAC,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,+DAA+D,EAAE,KAAK,CAAC,CAAC;QACvF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;IAEK,cAAc,GAAG,GAAS,EAAE;QACjC,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/C,OAAO,IAAI,CAAC,WAAW,CAAC,cAAc,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;YAC1C,MAAM,CAAC,KAAK,CAAC,iEAAiE,EAAE,KAAK,CAAC,CAAC;QACzF,CAAC,CAAC,CAAC;IACL,CAAC,CAAC;CACH"}