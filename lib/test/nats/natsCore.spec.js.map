{"version": 3, "file": "natsCore.spec.js", "sourceRoot": "", "sources": ["../../../src/test/nats/natsCore.spec.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,MAAM,yBAAyB,CAAC;AAC5D,OAAO,EAAE,UAAU,EAAE,MAAM,QAAQ,CAAC;AACpC,OAAO,EACL,QAAQ,EACR,SAAS,EACT,SAAS,EACT,UAAU,EACV,QAAQ,EACR,MAAM,EACN,EAAE,GACH,MAAM,QAAQ,CAAC;AAEhB,OAAO,OAAO,MAAM,0BAA0B,CAAC;AAC/C,OAAO,MAAM,MAAM,yBAAyB,CAAC;AAE7C,QAAQ,CAAC,iBAAiB,EAAE,GAAG,EAAE;IAC/B,6BAA6B;IAC7B,4BAA4B;IAE5B,IAAI,EAAuB,CAAC;IAC5B,8BAA8B;IAC9B,IAAI,mBAAmB,GAAwB,EAAE,CAAC;IAElD,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC;QAChC,MAAM,UAAU,GAAG,MAAM,EAAE,IAAI,IAAI,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,MAAM,CAAC,IAAI,EAAE,OAAO,CAAC,MAAM,GAAG,CAAC;YACvG,CAAC,CAAC,MAAM,CAAC,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,IAAI,uBAAuB;YACtD,CAAC,CAAC,uBAAuB,CAAC;QAE5B,IAAI,CAAC;YACH,EAAE,GAAG,MAAM,OAAO,CAAC;gBACjB,OAAO,EAAE,CAAC,UAAU,CAAC;gBACrB,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,IAAI,IAAI,oBAAoB,UAAU,EAAE,EAAE;gBAC9D,OAAO,EAAE,IAAI;gBACb,SAAS,EAAE,IAAI;gBACf,oBAAoB,EAAE,CAAC;aACxB,CAAC,CAAC;YAEH,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,KAAK,CAAC,CAAC;YACzD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,UAAU,CAAC,GAAG,EAAE;QACd,mBAAmB,GAAG,EAAE,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,SAAS,CAAC,KAAK,IAAI,EAAE;QACnB,mBAAmB,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,CAAC;QACtD,mBAAmB,GAAG,EAAE,CAAC;IAC3B,CAAC,CAAC,CAAC;IAEH,QAAQ,CAAC,KAAK,IAAI,EAAE;QAClB,IAAI,CAAC;YACH,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC;YACjB,MAAM,CAAC,KAAK,CAAC,+BAA+B,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,4DAA4D,EAAE,KAAK,IAAI,EAAE;QAC1E,MAAM,MAAM,GAAG,UAAU,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,QAAQ,EAAE,CAAC;QAElC,MAAM,OAAO,GAAG,GAAG,MAAM,UAAU,CAAC;QACpC,MAAM,gBAAgB,GAAU,EAAE,CAAC;QAEnC,MAAM,YAAY,GAAG,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE;YACzC,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrB,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,KAAK,CAAC,qBAAqB,EAAE,GAAG,CAAC,CAAC;oBACzC,OAAO;gBACT,CAAC;gBACD,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,wBAAwB,GAAG,CAAC,OAAO,IAAI,EAAE,IAAI,CAAC,CAAC;gBAC5D,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE5B,IAAI,gBAAgB,CAAC,MAAM,IAAI,EAAE,EAAE,CAAC;oBAClC,cAAc,CAAC,OAAO,EAAE,CAAC;gBAC3B,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEH,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,EAAE,CAAC;YAC7B,EAAE,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,SAAS,CAAC,EAAC,EAAE,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,EAAE,CAAC,EAAC,CAAC,CAAC,CAAC;YAEvE,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,EAAE,CAAC,CAAC,CAAC;QACxD,CAAC;QAED,MAAM,OAAO,CAAC,IAAI,CAAC;YACf,cAAc;YAChB,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;SACnE,CAAC,CAAC;QAEH,4BAA4B;QAC5B,YAAY,CAAC,WAAW,EAAE,CAAC;QAE3B,4CAA4C;QAC5C,MAAM,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACzC,MAAM,CAAC,gBAAgB,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC;IACtF,CAAC,CAAC,CAAC;IAEH,EAAE,CAAC,wEAAwE,EAAE,KAAK,IAAI,EAAE;QACtF,MAAM,MAAM,GAAG,UAAU,EAAE,CAAC;QAC5B,MAAM,cAAc,GAAG,QAAQ,EAAE,CAAC;QAElC,6CAA6C;QAC7C,IAAI,SAAS,GAAkB,IAAI,CAAC;QAEpC,0BAA0B;QAC1B,MAAM,cAAc,GAAU,EAAE,CAAC;QACjC,MAAM,eAAe,GAAU,EAAE,CAAC;QAElC,sCAAsC;QACtC,MAAM,aAAa,GAAG,GAAG,MAAM,UAAU,CAAC;QAC1C,MAAM,SAAS,GAAG,EAAE,CAAC,SAAS,CAAC,aAAa,EAAE;YAC5C,QAAQ,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;gBACrB,IAAI,GAAG,EAAE,CAAC;oBACR,MAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,GAAG,CAAC,CAAC;oBACnD,OAAO;gBACT,CAAC;gBAED,MAAM,IAAI,GAAG,GAAG,CAAC,IAAI,EAAE,CAAC;gBACxB,MAAM,CAAC,KAAK,CAAC,sBAAsB,GAAG,CAAC,OAAO,GAAG,EAAE,IAAI,CAAC,CAAC;gBACzD,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAE1B,4CAA4C;gBAC5C,IAAI,IAAI,IAAI,OAAO,IAAI,KAAK,QAAQ,IAAI,WAAW,IAAI,IAAI,EAAE,CAAC;oBAC5D,SAAS,GAAG,IAAI,CAAC,SAAmB,CAAC;gBACvC,CAAC;gBAED,0CAA0C;gBAC1C,IAAI,SAAS,EAAE,CAAC;oBACd,MAAM,cAAc,GAAG,GAAG,MAAM,IAAI,SAAS,EAAE,CAAC;oBAChD,MAAM,CAAC,KAAK,CAAC,2BAA2B,cAAc,EAAE,CAAC,CAAC;oBAE1D,MAAM,UAAU,GAAG,EAAE,CAAC,SAAS,CAAC,cAAc,EAAE;wBAC9C,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;4BACzB,IAAI,KAAK,EAAE,CAAC;gCACV,MAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;gCACtD,OAAO;4BACT,CAAC;4BAED,MAAM,MAAM,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC;4BAC5B,MAAM,CAAC,KAAK,CAAC,+BAA+B,KAAK,CAAC,OAAO,GAAG,EAAE,MAAM,CAAC,CAAC;4BACtE,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;4BAE7B,4DAA4D;4BAC5D,IAAI,eAAe,CAAC,MAAM,IAAI,CAAC,EAAE,CAAC;gCAChC,UAAU,CAAC,WAAW,EAAE,CAAC;gCACzB,cAAc,CAAC,OAAO,EAAE,CAAC;4BAC3B,CAAC;wBACH,CAAC;qBACF,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;SACF,CAAC,CAAC;QAEH,2CAA2C;QAC3C,MAAM,aAAa,GAAG,UAAU,EAAE,CAAC;QACnC,EAAE,CAAC,OAAO,CAAC,aAAa,EAAE,IAAI,CAAC,SAAS,CAAC;YACvC,IAAI,EAAE,QAAQ;YACd,SAAS,EAAE,aAAa;YACxB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC,CAAC;QAEJ,4DAA4D;QAC5D,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAEvD,wCAAwC;QACxC,MAAM,cAAc,GAAG,GAAG,MAAM,IAAI,aAAa,EAAE,CAAC;QACpD,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;YAC5B,EAAE,CAAC,OAAO,CAAC,cAAc,EAAE,IAAI,CAAC,SAAS,CAAC;gBACxC,SAAS,EAAE,CAAC;gBACZ,OAAO,EAAE,mBAAmB,CAAC,EAAE;gBAC/B,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC,CAAC;QACN,CAAC;QAED,2CAA2C;QAC3C,MAAM,OAAO,CAAC,IAAI,CAAC;YACjB,cAAc;YACd,kCAAkC;YAClC,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,EAAE,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,CAAC;SACnE,CAAC,CAAC;QAEH,4BAA4B;QAC5B,SAAS,CAAC,WAAW,EAAE,CAAC;QAExB,aAAa;QACb,MAAM,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,sBAAsB,CAAC,CAAC,CAAC,CAAC;QACxD,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACxD,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;QACvC,MAAM,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IACvE,CAAC,CAAC,CAAC;AACL,CAAC,CAAC,CAAC"}