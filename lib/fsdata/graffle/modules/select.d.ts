import type * as $$Utilities from 'graffle/utilities-for-generated';
import type { OperationTypeNode } from 'graphql';
import * as $$Schema from './schema.js';
import * as $$SelectionSets from './selection-sets.js';
export declare const Select: import("./methods-select.js").$MethodsSelect;
export declare namespace Select {
    type Query<$SelectionSet extends $$SelectionSets.Query> = $$Utilities.DocumentBuilder.InferResult.Operation<$SelectionSet, $$Schema.Schema, OperationTypeNode.QUERY>;
    type Mutation<$SelectionSet extends $$SelectionSets.Mutation> = $$Utilities.DocumentBuilder.InferResult.Operation<$SelectionSet, $$Schema.Schema, OperationTypeNode.MUTATION>;
    type Subscription<$SelectionSet extends $$SelectionSets.Subscription> = $$Utilities.DocumentBuilder.InferResult.Operation<$SelectionSet, $$Schema.Schema, OperationTypeNode.SUBSCRIPTION>;
    type UserMetadata<$SelectionSet extends $$SelectionSets.UserMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserMetadata']>;
    type ChannelsUserMetadata<$SelectionSet extends $$SelectionSets.ChannelsUserMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelsUserMetadata']>;
    type GroupsUserMetadata<$SelectionSet extends $$SelectionSets.GroupsUserMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['GroupsUserMetadata']>;
    type GroupMembership<$SelectionSet extends $$SelectionSets.GroupMembership> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['GroupMembership']>;
    type ModelEvent<$SelectionSet extends $$SelectionSets.ModelEvent> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ModelEvent']>;
    type Expertise<$SelectionSet extends $$SelectionSets.Expertise> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Expertise']>;
    type Option<$SelectionSet extends $$SelectionSets.Option> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Option']>;
    type Industry<$SelectionSet extends $$SelectionSets.Industry> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Industry']>;
    type MenteesGroupMembership<$SelectionSet extends $$SelectionSets.MenteesGroupMembership> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['MenteesGroupMembership']>;
    type MentorsGroupMembership<$SelectionSet extends $$SelectionSets.MentorsGroupMembership> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['MentorsGroupMembership']>;
    type MastercardGroupMembership<$SelectionSet extends $$SelectionSets.MastercardGroupMembership> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['MastercardGroupMembership']>;
    type IqlaaGroupMembership<$SelectionSet extends $$SelectionSets.IqlaaGroupMembership> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['IqlaaGroupMembership']>;
    type StriveIndonesiaGroupMembership<$SelectionSet extends $$SelectionSets.StriveIndonesiaGroupMembership> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['StriveIndonesiaGroupMembership']>;
    type CompanyStage<$SelectionSet extends $$SelectionSets.CompanyStage> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['CompanyStage']>;
    type CompanyType<$SelectionSet extends $$SelectionSets.CompanyType> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['CompanyType']>;
    type EducationLevel<$SelectionSet extends $$SelectionSets.EducationLevel> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['EducationLevel']>;
    type Gender<$SelectionSet extends $$SelectionSets.Gender> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Gender']>;
    type Pronoun<$SelectionSet extends $$SelectionSets.Pronoun> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Pronoun']>;
    type UserInbox<$SelectionSet extends $$SelectionSets.UserInbox> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserInbox']>;
    type ChannelInbox<$SelectionSet extends $$SelectionSets.ChannelInbox> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelInbox']>;
    type ChannelInboxItemMessage<$SelectionSet extends $$SelectionSets.ChannelInboxItemMessage> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelInboxItemMessage']>;
    type ChannelInboxItemInvitation<$SelectionSet extends $$SelectionSets.ChannelInboxItemInvitation> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelInboxItemInvitation']>;
    type User<$SelectionSet extends $$SelectionSets.User> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['User']>;
    type LabeledStringValue<$SelectionSet extends $$SelectionSets.LabeledStringValue> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['LabeledStringValue']>;
    type UserPreferences<$SelectionSet extends $$SelectionSets.UserPreferences> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserPreferences']>;
    type NotificationOptions<$SelectionSet extends $$SelectionSets.NotificationOptions> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['NotificationOptions']>;
    type UserDeviceWithoutAuth<$SelectionSet extends $$SelectionSets.UserDeviceWithoutAuth> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserDeviceWithoutAuth']>;
    type UserBlock<$SelectionSet extends $$SelectionSets.UserBlock> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserBlock']>;
    type Contact<$SelectionSet extends $$SelectionSets.Contact> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Contact']>;
    type ContactMetadata<$SelectionSet extends $$SelectionSets.ContactMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ContactMetadata']>;
    type ContactType<$SelectionSet extends $$SelectionSets.ContactType> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ContactType']>;
    type Company<$SelectionSet extends $$SelectionSets.Company> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Company']>;
    type AcademicExperience<$SelectionSet extends $$SelectionSets.AcademicExperience> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['AcademicExperience']>;
    type BusinessExperience<$SelectionSet extends $$SelectionSets.BusinessExperience> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BusinessExperience']>;
    type UploadedAsset<$SelectionSet extends $$SelectionSets.UploadedAsset> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UploadedAsset']>;
    type UserProfileRoleHistoryItem<$SelectionSet extends $$SelectionSets.UserProfileRoleHistoryItem> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserProfileRoleHistoryItem']>;
    type Country<$SelectionSet extends $$SelectionSets.Country> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Country']>;
    type Language<$SelectionSet extends $$SelectionSets.Language> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Language']>;
    type Notification<$SelectionSet extends $$SelectionSets.Notification> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Notification']>;
    type NotificationContext<$SelectionSet extends $$SelectionSets.NotificationContext> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['NotificationContext']>;
    type Channel<$SelectionSet extends $$SelectionSets.Channel> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Channel']>;
    type ChannelMetadata<$SelectionSet extends $$SelectionSets.ChannelMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelMetadata']>;
    type BgLatestUnseenChannelMessageInfo<$SelectionSet extends $$SelectionSets.BgLatestUnseenChannelMessageInfo> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BgLatestUnseenChannelMessageInfo']>;
    type BgChannelStatus<$SelectionSet extends $$SelectionSets.BgChannelStatus> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BgChannelStatus']>;
    type ChannelInvitation<$SelectionSet extends $$SelectionSets.ChannelInvitation> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelInvitation']>;
    type DeclineChannelInvitationReason<$SelectionSet extends $$SelectionSets.DeclineChannelInvitationReason> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['DeclineChannelInvitationReason']>;
    type ChannelMessage<$SelectionSet extends $$SelectionSets.ChannelMessage> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelMessage']>;
    type ChannelMessageMetadata<$SelectionSet extends $$SelectionSets.ChannelMessageMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelMessageMetadata']>;
    type ChannelMessageStatus<$SelectionSet extends $$SelectionSets.ChannelMessageStatus> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelMessageStatus']>;
    type ChannelParticipant<$SelectionSet extends $$SelectionSets.ChannelParticipant> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ChannelParticipant']>;
    type BgChannelParticipantMetadata<$SelectionSet extends $$SelectionSets.BgChannelParticipantMetadata> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BgChannelParticipantMetadata']>;
    type UserListItem<$SelectionSet extends $$SelectionSets.UserListItem> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserListItem']>;
    type EndorsementWithTypes<$SelectionSet extends $$SelectionSets.EndorsementWithTypes> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['EndorsementWithTypes']>;
    type ModerationConcern<$SelectionSet extends $$SelectionSets.ModerationConcern> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ModerationConcern']>;
    type ContentTagType<$SelectionSet extends $$SelectionSets.ContentTagType> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ContentTagType']>;
    type Group<$SelectionSet extends $$SelectionSets.Group> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Group']>;
    type AppliedGroupRule<$SelectionSet extends $$SelectionSets.AppliedGroupRule> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['AppliedGroupRule']>;
    type GroupRuleBaseConfig<$SelectionSet extends $$SelectionSets.GroupRuleBaseConfig> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['GroupRuleBaseConfig']>;
    type AdminTask<$SelectionSet extends $$SelectionSets.AdminTask> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['AdminTask']>;
    type AdminTaskDef<$SelectionSet extends $$SelectionSets.AdminTaskDef> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['AdminTaskDef']>;
    type AdminTaskArgDef<$SelectionSet extends $$SelectionSets.AdminTaskArgDef> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['AdminTaskArgDef']>;
    type ErrorCodeOption<$SelectionSet extends $$SelectionSets.ErrorCodeOption> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ErrorCodeOption']>;
    type IndonesianCity<$SelectionSet extends $$SelectionSets.IndonesianCity> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['IndonesianCity']>;
    type IndonesianProvince<$SelectionSet extends $$SelectionSets.IndonesianProvince> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['IndonesianProvince']>;
    type IqlaaJordanianDistrict<$SelectionSet extends $$SelectionSets.IqlaaJordanianDistrict> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['IqlaaJordanianDistrict']>;
    type IqlaaJordanianGovernorate<$SelectionSet extends $$SelectionSets.IqlaaJordanianGovernorate> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['IqlaaJordanianGovernorate']>;
    type MastercardBank<$SelectionSet extends $$SelectionSets.MastercardBank> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['MastercardBank']>;
    type UserSearch<$SelectionSet extends $$SelectionSets.UserSearch> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserSearch']>;
    type UserSearchFilter<$SelectionSet extends $$SelectionSets.UserSearchFilter> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserSearchFilter']>;
    type UserSearchRunInfo<$SelectionSet extends $$SelectionSets.UserSearchRunInfo> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserSearchRunInfo']>;
    type UserWithScore<$SelectionSet extends $$SelectionSets.UserWithScore> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserWithScore']>;
    type ServiceRequest<$SelectionSet extends $$SelectionSets.ServiceRequest> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ServiceRequest']>;
    type ContactListItem<$SelectionSet extends $$SelectionSets.ContactListItem> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ContactListItem']>;
    type SidMultiStepAction<$SelectionSet extends $$SelectionSets.SidMultiStepAction> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['SidMultiStepAction']>;
    type MultiStepActionError<$SelectionSet extends $$SelectionSets.MultiStepActionError> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['MultiStepActionError']>;
    type SidMultiStepActionProgress<$SelectionSet extends $$SelectionSets.SidMultiStepActionProgress> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['SidMultiStepActionProgress']>;
    type MyUser<$SelectionSet extends $$SelectionSets.MyUser> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['MyUser']>;
    type ReportUserReason<$SelectionSet extends $$SelectionSets.ReportUserReason> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ReportUserReason']>;
    type Training<$SelectionSet extends $$SelectionSets.Training> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['Training']>;
    type TrainingContentPage<$SelectionSet extends $$SelectionSets.TrainingContentPage> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['TrainingContentPage']>;
    type TrainingSession<$SelectionSet extends $$SelectionSets.TrainingSession> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['TrainingSession']>;
    type TrainingSessionCompletionInfo<$SelectionSet extends $$SelectionSets.TrainingSessionCompletionInfo> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['TrainingSessionCompletionInfo']>;
    type UserAuthResponse<$SelectionSet extends $$SelectionSets.UserAuthResponse> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['UserAuthResponse']>;
    type ContentTag<$SelectionSet extends $$SelectionSets.ContentTag> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ContentTag']>;
    type SupportChannelConfig<$SelectionSet extends $$SelectionSets.SupportChannelConfig> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['SupportChannelConfig']>;
    type NotificationTemplate<$SelectionSet extends $$SelectionSets.NotificationTemplate> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['NotificationTemplate']>;
    type ContentStatus<$SelectionSet extends $$SelectionSets.ContentStatus> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ContentStatus']>;
    type BgChannelChangedEvent<$SelectionSet extends $$SelectionSets.BgChannelChangedEvent> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BgChannelChangedEvent']>;
    type ObjectChangedEvent<$SelectionSet extends $$SelectionSets.ObjectChangedEvent> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['ObjectChangedEvent']>;
    type BaseModel<$SelectionSet extends $$SelectionSets.BaseModel> = $$Utilities.DocumentBuilder.InferResult.OutputObjectLike<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BaseModel']>;
    type BaseModelMetadata<$SelectionSet extends $$SelectionSets.BaseModelMetadata> = $$Utilities.DocumentBuilder.InferResult.Interface<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['BaseModelMetadata']>;
    type IGroupMembership<$SelectionSet extends $$SelectionSets.IGroupMembership> = $$Utilities.DocumentBuilder.InferResult.Interface<$SelectionSet, $$Schema.Schema, $$Schema.Schema['allTypes']['IGroupMembership']>;
}
